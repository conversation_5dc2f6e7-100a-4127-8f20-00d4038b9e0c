<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Signedtrue</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="An easy-to-use web application that lets you fill out, edit, and sign PDF documents anytime you need to!"
    />
    <meta property="og:title" content="Signedtrue" />
    <meta
      property="og:description"
      content="An easy-to-use web application that lets you fill out, edit, and sign PDF documents anytime you need to!"
    />
    <meta property="og:url" content="https://www.signedtrue.com/excuse-note/" />

    <!-- DNS prefetch for external domains -->
    <link rel="dns-prefetch" href="//app.signedtrue.com">
    <link rel="dns-prefetch" href="//www.signedtrue.com">

    <!-- Preload critical images -->
    <link
      rel="preload"
      as="image"
      href="./signedtrue-hd-white-transparent.webp"
      type="image/webp"
    />

    <link
      rel="preload"
      as="image"
      href="./doctors-note-1.webp"
      type="image/webp"
    />

    <!-- Preload critical fonts -->
    <link rel="preload" href="./fonts/poppins-v23-latin-regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="./fonts/poppins-v23-latin-600.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Critical CSS inlined for above-the-fold content -->
    <style>
      /* Critical CSS - Above the fold styles */
      *,::before,::after{box-sizing:border-box}
      html,body{box-sizing:border-box;height:100%;margin:0;padding:0;overflow:hidden}
      body{background:#fff;font-style:normal;-webkit-font-smoothing:antialiased;font-weight:400;line-height:1.6;color:#333333;background-color:#ffffff;font-family:"Poppins",Sans-serif;font-size:18px;position:relative;hyphens:manual}
      @font-face{font-display:swap;font-family:"Poppins";font-style:normal;font-weight:400;src:url("./fonts/poppins-v23-latin-regular.woff2") format("woff2")}
      @font-face{font-display:swap;font-family:"Poppins";font-style:normal;font-weight:600;src:url("./fonts/poppins-v23-latin-600.woff2") format("woff2")}
      .overlay{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;z-index:9999}
      .overlay-header{width:100%;padding:15px 150px;background:#212121;text-align:left;position:relative;z-index:1;flex-shrink:0}
      .overlay-header img{vertical-align:middle;border:none;border-radius:0;box-shadow:none;height:50px;max-width:200px}
      .content-scroll-area{flex:1;position:relative}
      .bg-image{vertical-align:middle;position:absolute;top:0;left:0;width:100%;background-size:cover;background-position:top center;z-index:-1;height:auto;max-width:100%;border:none;border-radius:0;box-shadow:none}
      .overlay-color{position:absolute;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,0.6);z-index:-1}
      .overlay-content-wrapper{min-height:100%;display:flex;align-items:center;justify-content:center;padding:20px 0}
      .overlay-content{text-align:center;color:#fff;max-width:500px;padding:20px;z-index:1}
      .overlay-content h1{line-height:1.2;font-weight:600;font-size:36px;margin-bottom:10px;color:white}
      .overlay-content p{font-size:36px;margin-bottom:10px}
      .overlay-content p.sub-text{font-size:14px;margin-bottom:42px}
      .overlay-content a.button{text-shadow:none;padding:15px 40px;box-shadow:none;background:#2563eb;color:#fff;max-height:50px;max-width:250px;text-decoration:none;border-radius:5px;font-size:20px;font-weight:600;transition:background 0.3s;border:1px solid;line-height:50px;display:flex;justify-content:center;align-items:center}
      @media (max-width:768px){.overlay-header{padding:15px 15px!important;height:65px}.overlay-header img{max-height:100%;width:auto}}
    </style>

    <!-- Load non-critical CSS asynchronously -->
    <link rel="preload" href="./style.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="./style.css"></noscript>

    <!-- CSS loading fallback script -->
    <script src="./css-loader.js" async></script>
  </head>
  <body>
    <div class="overlay">
      <div class="overlay-header">
        <img
          decoding="async"
          src="./signedtrue-hd-white-transparent.webp"
          alt="Logo"
          height="50px"
          width="100%"
        />
      </div>

      <div class="content-scroll-area">
        <img
          decoding="async"
          src="./doctors-note-1.webp"
          class="bg-image"
          alt="Background"
        />
        <div class="overlay-color"></div>

        <div class="overlay-content-wrapper">
          <div class="overlay-content">
            <p>Get Your</p>
            <h1>Excuse Note</h1>
            <p>in Minutes</p>
            <p class="sub-text">Quick, Reliable, and Hassle-free.</p>
            <a href="https://app.signedtrue.com/wizard/note/" class="button"
              >GET STARTED</a
            >
          </div>
        </div>
      </div>

      <div class="overlay-footer">
        <p class="copyright">Copyright © 2025 signedtrue.com</p>
        <p>
          <a href="https://www.signedtrue.com/privacy-policy/"
            >Privacy Policy</a
          >
          |
          <a href="https://www.signedtrue.com/terms-and-conditions/"
            >Terms of Use</a
          >
        </p>
      </div>
    </div>

    <!-- Non-blocking JavaScript moved to bottom -->
    <script>
      // Cookie management functions - moved to bottom to avoid blocking
      function getCookie(name) {
        let cookieArr = document.cookie.split(";");
        for (let i = 0; i < cookieArr.length; i++) {
          let cookie = cookieArr[i].trim();
          if (cookie.startsWith(name + "=")) {
            return cookie.substring(name.length + 1);
          }
        }
        return null;
      }

      function setCookie(name, value, days) {
        let expires = "";
        if (days) {
          let date = new Date();
          date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + value + expires + "; path=/; domain=.signedtrue.com";
      }

      // Initialize flags after DOM is ready
      document.addEventListener("DOMContentLoaded", function () {
        let flags = {
          flow: "02",
          ppg: "02",
        };

        Object.keys(flags).forEach((key) => {
          let cookieValue = getCookie(key);
          if (!cookieValue) {
            setCookie(key, flags[key], 7);
          }
        });

        // Remove cookie banner if present
        var cookieBanner = document.getElementById("cookie-law-info-bar");
        if (cookieBanner) {
          cookieBanner.remove();
        }
      });
    </script>
  </body>
</html>
