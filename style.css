*,
::before,
::after {
    box-sizing: border-box;
}

a,
abbr,
acronym,
address,
applet,
big,
blockquote,
body,
caption,
cite,
code,
dd,
del,
dfn,
div,
dl,
dt,
em,
fieldset,
font,
form,
h1,
h2,
h3,
h4,
h5,
h6,
html,
iframe,
ins,
kbd,
label,
legend,
li,
object,
ol,
p,
pre,
q,
s,
samp,
small,
span,
strike,
strong,
sub,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
tr,
tt,
ul,
var {
    border: 0;
    font-size: 100%;
    font-style: inherit;
    font-weight: inherit;
    margin: 0;
    outline: 0;
    padding: 0;
    vertical-align: baseline;
}

html,
body {
    box-sizing: border-box;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    /* Prevent page-level scrolling */
}

body {
    background: #fff;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    font-weight: 400;
    line-height: 1.6;
    color: #333333;
    background-color: #ffffff;
    background-image: none;

    font-family: "Poppins", Sans-serif;
    font-size: 18px;
    position: relative;

    hyphens: manual;

}

@font-face {
    font-display: swap;
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    src: url("./fonts/poppins-v23-latin-regular.woff2") format("woff2"),
        url("./fonts/poppins-v23-latin-regular.ttf") format("truetype");
}

@font-face {
    font-display: swap;
    font-family: "Poppins";
    font-style: normal;
    font-weight: 600;
    src: url("./fonts/poppins-v23-latin-600.woff2") format("woff2"),
        url("./fonts/poppins-v23-latin-600.ttf") format("truetype");
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 9999;
}

.overlay-color {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: -1;
}

.overlay-header {
    width: 100%;
    padding: 15px 150px;
    background: #212121;
    text-align: left;
    position: relative;
    z-index: 1;
    flex-shrink: 0;
}

.overlay-header img {
    vertical-align: middle;
    border: none;
    border-radius: 0;
    box-shadow: none;
    height: 50px;
    max-width: 200px;
}

.content-scroll-area {
    flex: 1;
    position: relative;
}

.bg-image {
    vertical-align: middle;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background-size: cover;
    background-position: top center;
    z-index: -1;
    height: auto;
    max-width: 100%;
    border: none;
    border-radius: 0;
    box-shadow: none;
}

.overlay-content-wrapper {
    min-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
}

.overlay-content {
    text-align: center;
    color: #fff;
    max-width: 500px;
    padding: 20px;
    z-index: 1;
}

.overlay-content h1 {
    line-height: 1.2;
    font-weight: 600;
    font-size: 36px;
    margin-bottom: 10px;
    color: white;
}

.overlay-content p {
    font-size: 36px;
    margin-bottom: 10px;
}

.overlay-content p.sub-text {
    font-size: 14px;
    margin-bottom: 42px;
}

.overlay-content a.button {
    text-shadow: none;
    padding-top: 15px;
    padding-right: 40px;
    padding-bottom: 15px;
    padding-left: 40px;
    box-shadow: none;
    background: #2563eb;
    color: #fff;
    max-height: 50px;
    max-width: 250px;
    text-decoration: none;
    border-radius: 5px;
    font-size: 20px;
    font-weight: 600;
    transition: background 0.3s;
    border: 1px solid;
    line-height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.overlay-content a.button:hover {
    background: #1d4ed8;
}

.overlay-footer {
    height: 70px;
    width: 100%;
    padding: 10px 150px;
    background: #212121;
    text-align: center;
    color: white;
    flex-shrink: 0;
    z-index: 10;
}

.overlay-footer p.copyright {
    font-size: 14px;
    margin: 0;
}

.overlay-footer p {
    margin-bottom: 1.75em;
}

.overlay-footer a {
    background-color: transparent;
    box-shadow: none;
    color: #878686;
    text-decoration: none;
    margin: 0 10px;
    font-size: 14px;
    transition: color 0.3s;
}

.overlay-footer a:hover {
    color: #23527c;
    text-decoration: underline;
}

/* Desktop-only styles */
@media (min-width: 1024px) {
    .bg-image {
        margin-top: -86px;
    }
}

/* Mobile optimization */
@media (max-width: 768px) {
    .overlay-header {
        padding: 15px 15px !important;
        height: 65px;
    }

    .overlay-header img {
        max-height: 100%;
        width: auto;
    }

    .overlay-footer {
        padding: 10px;
    }

    .bg-image {
        margin-top: 30px;
        object-position: top center;
        object-fit: cover;
        background-size: cover;
    }
}