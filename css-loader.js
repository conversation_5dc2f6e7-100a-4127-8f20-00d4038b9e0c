// CSS loading fallback for browsers that don't support onload on link elements
(function() {
  'use strict';
  
  // Check if browser supports onload on link elements
  var supportsOnload = 'onload' in document.createElement('link');
  
  if (!supportsOnload) {
    // Fallback for older browsers
    var links = document.querySelectorAll('link[rel="preload"][as="style"]');
    for (var i = 0; i < links.length; i++) {
      var link = links[i];
      link.rel = 'stylesheet';
    }
  }
})();
